# Arien AI CLI Universal Installation Script for Windows PowerShell
# Supports Windows 10/11 with PowerShell 5.1+ or PowerShell Core

param(
    [Parameter(Position=0)]
    [ValidateSet("install", "update", "uninstall", "verify", "help")]
    [string]$Action = "install",
    
    [switch]$Force,
    [switch]$Global = $true
)

# Configuration
$RepoUrl = "https://github.com/arien-ai/arien-ai-cli.git"
$InstallDir = "$env:USERPROFILE\.arien-ai"
$BinName = "arien"
$MinNodeVersion = 22

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    White = "White"
    Gray = "Gray"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White",
        [string]$Prefix = ""
    )
    
    if ($Prefix) {
        Write-Host "[$Prefix] " -ForegroundColor $Color -NoNewline
    }
    Write-Host $Message -ForegroundColor $Color
}

function Write-Status { param([string]$Message) Write-ColorOutput $Message "Blue" "INFO" }
function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" "SUCCESS" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" "WARNING" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" "ERROR" }

function Show-Header {
    Write-Host ""
    Write-ColorOutput "╔═══════════════════════════════════════╗" "Cyan"
    Write-ColorOutput "║        🤖 ARIEN AI CLI INSTALLER      ║" "Cyan"
    Write-ColorOutput "║     Universal Installation Script     ║" "Cyan"
    Write-ColorOutput "╚═══════════════════════════════════════╝" "Cyan"
    Write-Host ""
}

function Test-CommandExists {
    param([string]$Command)
    $null = Get-Command $Command -ErrorAction SilentlyContinue
    return $?
}

function Get-SystemInfo {
    Write-Status "System Information:"
    Write-Host "   Platform: Windows $([Environment]::OSVersion.Version)"
    Write-Host "   Architecture: $env:PROCESSOR_ARCHITECTURE"
    Write-Host "   PowerShell: $($PSVersionTable.PSVersion)"
    Write-Host "   User: $env:USERNAME"
    Write-Host "   Home: $env:USERPROFILE"
    Write-Host ""
}

function Test-Prerequisites {
    Write-Status "Checking prerequisites..."
    
    # Check Node.js
    if (-not (Test-CommandExists "node")) {
        Write-Error "Node.js is not installed"
        Write-Status "Please install Node.js $MinNodeVersion or higher from https://nodejs.org/"
        exit 1
    }
    
    # Check Node.js version
    $nodeVersionOutput = node -v
    $nodeVersion = [int]($nodeVersionOutput -replace 'v(\d+)\..*', '$1')
    
    if ($nodeVersion -lt $MinNodeVersion) {
        Write-Error "Node.js version $MinNodeVersion or higher is required"
        Write-Status "Current version: $nodeVersionOutput"
        Write-Status "Please update Node.js from https://nodejs.org/"
        exit 1
    }
    
    Write-Success "Node.js $nodeVersionOutput is compatible"
    
    # Check npm
    if (-not (Test-CommandExists "npm")) {
        Write-Error "npm is not installed"
        Write-Status "npm should come with Node.js. Please reinstall Node.js"
        exit 1
    }
    
    $npmVersion = npm -v
    Write-Success "npm $npmVersion is available"
    
    # Check git
    if (-not (Test-CommandExists "git")) {
        Write-Error "git is not installed"
        Write-Status "Please install git from https://git-scm.com/"
        exit 1
    }
    
    $gitVersion = (git --version) -replace 'git version ', ''
    Write-Success "git $gitVersion is available"
}

function Install-ArienAI {
    Write-Status "Starting Arien AI CLI installation..."
    
    # Remove existing installation if it exists
    if (Test-Path $InstallDir) {
        if ($Force) {
            Write-Warning "Removing existing installation..."
            Remove-Item $InstallDir -Recurse -Force
        } else {
            $response = Read-Host "Existing installation found. Remove it? (y/N)"
            if ($response -match '^[Yy]$') {
                Remove-Item $InstallDir -Recurse -Force
            } else {
                Write-Status "Installation cancelled."
                exit 0
            }
        }
    }
    
    # Clone repository
    Write-Status "Cloning repository..."
    try {
        git clone $RepoUrl $InstallDir
        Set-Location $InstallDir
    } catch {
        Write-Error "Failed to clone repository: $_"
        exit 1
    }
    
    # Install dependencies
    Write-Status "Installing dependencies..."
    try {
        npm install
    } catch {
        Write-Error "Failed to install dependencies: $_"
        exit 1
    }
    
    # Build project
    Write-Status "Building project..."
    try {
        npm run build
    } catch {
        Write-Error "Failed to build project: $_"
        exit 1
    }
    
    # Install globally
    if ($Global) {
        Write-Status "Installing globally..."
        try {
            npm install -g .
        } catch {
            Write-Error "Failed to install globally: $_"
            Write-Status "You may need to run PowerShell as Administrator"
            exit 1
        }
    }
    
    Write-Success "Installation completed successfully!"
}

function Update-ArienAI {
    Write-Status "Updating Arien AI CLI..."
    
    if (-not (Test-Path $InstallDir)) {
        Write-Warning "No existing installation found. Installing instead..."
        Install-ArienAI
        return
    }
    
    Set-Location $InstallDir
    
    # Pull latest changes
    Write-Status "Pulling latest changes..."
    try {
        git pull origin main
    } catch {
        Write-Error "Failed to pull latest changes: $_"
        exit 1
    }
    
    # Install dependencies
    Write-Status "Installing dependencies..."
    npm install
    
    # Build project
    Write-Status "Building project..."
    npm run build
    
    # Install globally
    if ($Global) {
        Write-Status "Installing globally..."
        npm install -g .
    }
    
    Write-Success "Update completed successfully!"
}

function Uninstall-ArienAI {
    Write-Status "Uninstalling Arien AI CLI..."
    
    $response = Read-Host "Are you sure you want to uninstall Arien AI CLI? (y/N)"
    if ($response -notmatch '^[Yy]$') {
        Write-Status "Uninstallation cancelled."
        exit 0
    }
    
    # Uninstall global package
    try {
        npm uninstall -g arien-ai-cli 2>$null
    } catch {
        # Ignore errors - package might not be installed globally
    }
    
    # Remove installation directory
    if (Test-Path $InstallDir) {
        Remove-Item $InstallDir -Recurse -Force
    }
    
    Write-Success "Uninstallation completed successfully!"
}

function Test-Installation {
    Write-Status "Verifying installation..."
    
    if (Test-CommandExists "arien") {
        try {
            $version = arien --version 2>$null
            if (-not $version) { $version = "unknown" }
            Write-Success "Arien AI CLI $version is ready!"
            return $true
        } catch {
            Write-Error "Installation verification failed"
            return $false
        }
    } else {
        Write-Error "Installation verification failed"
        Write-Status "The 'arien' command is not available in PATH"
        return $false
    }
}

function Show-UsageInstructions {
    Write-ColorOutput "🎉 Getting Started:" "Cyan"
    Write-Host ""
    Write-Host "1. Run the CLI:"
    Write-Host "   arien"
    Write-Host ""
    Write-Host "2. Configure your LLM provider:"
    Write-Host "   arien config"
    Write-Host ""
    Write-Host "3. Test the connection:"
    Write-Host "   arien test"
    Write-Host ""
    Write-Host "4. Get help:"
    Write-Host "   arien --help"
    Write-Host ""
    Write-ColorOutput "💡 First-time Setup:" "Yellow"
    Write-Host "   • Choose between Deepseek (cloud) or Ollama (local) providers"
    Write-Host "   • For Deepseek: Get your API key from https://platform.deepseek.com"
    Write-Host "   • For Ollama: Make sure Ollama is installed and running locally"
    Write-Host ""
    Write-ColorOutput "🚀 Example Commands:" "Green"
    Write-Host "   `"Create a new TypeScript project`""
    Write-Host "   `"Find all TODO comments in my code`""
    Write-Host "   `"Install the latest version of express`""
    Write-Host "   `"Search for Node.js best practices`""
    Write-Host ""
}

function Show-Help {
    Write-Host "Arien AI CLI Installation Script for Windows PowerShell"
    Write-Host ""
    Write-Host "Usage: .\install.ps1 [Action] [Options]"
    Write-Host ""
    Write-Host "Actions:"
    Write-Host "  install   - Install Arien AI CLI (default)"
    Write-Host "  update    - Update existing installation"
    Write-Host "  uninstall - Remove Arien AI CLI"
    Write-Host "  verify    - Verify installation"
    Write-Host "  help      - Show this help message"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Force    - Force installation without prompts"
    Write-Host "  -Global   - Install globally (default: true)"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install.ps1"
    Write-Host "  .\install.ps1 install -Force"
    Write-Host "  .\install.ps1 update"
    Write-Host "  .\install.ps1 uninstall"
}

# Main execution
try {
    Show-Header
    Get-SystemInfo
    
    switch ($Action.ToLower()) {
        "install" {
            Test-Prerequisites
            Install-ArienAI
            if (Test-Installation) {
                Show-UsageInstructions
            }
        }
        "update" {
            Test-Prerequisites
            Update-ArienAI
            Test-Installation
        }
        "uninstall" {
            Uninstall-ArienAI
        }
        "verify" {
            Test-Installation
        }
        "help" {
            Show-Help
        }
        default {
            Write-Error "Unknown action: $Action"
            Show-Help
            exit 1
        }
    }
} catch {
    Write-Error "Installation failed: $_"
    exit 1
}
